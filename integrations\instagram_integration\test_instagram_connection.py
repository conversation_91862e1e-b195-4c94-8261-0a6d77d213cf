#!/usr/bin/env python3
"""
Test script to debug Instagram Unipile API connection
"""

import requests
import json
import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_unipile_api(api_key: str):
    """Test Unipile API connection and account retrieval for Instagram"""
    
    print(f"🔍 Testing Unipile API connection for Instagram...")
    print(f"API Key: {api_key[:10]}...{api_key[-10:]}")
    
    base_url = "https://api8.unipile.com:13814/api/v1"
    headers = {
        "X-API-KEY": api_key,
        "accept": "application/json",
        "Content-Type": "application/json"
    }
    
    try:
        # Test 1: Get accounts
        print("\n📋 Test 1: Getting accounts...")
        response = requests.get(f"{base_url}/accounts", headers=headers, timeout=30)
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.content:
            try:
                data = response.json()
                print(f"Response Data: {json.dumps(data, indent=2)}")
                
                # Check for accounts
                all_accounts = data.get("items", data.get("accounts", []))
                instagram_accounts = [acc for acc in all_accounts if acc.get("type") == "INSTAGRAM"]
                
                print(f"\n📊 Summary:")
                print(f"Total accounts: {len(all_accounts)}")
                print(f"Instagram accounts: {len(instagram_accounts)}")
                
                if instagram_accounts:
                    print(f"\n✅ Instagram accounts found:")
                    for acc in instagram_accounts:
                        print(f"  - ID: {acc.get('id')}")
                        print(f"    Username: @{acc.get('username', 'N/A')}")
                        print(f"    Name: {acc.get('name', 'N/A')}")
                        print(f"    Provider: {acc.get('provider', acc.get('type', 'N/A'))}")
                        print()
                else:
                    print(f"\n❌ No Instagram accounts found")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                print(f"Raw response: {response.text}")
        else:
            print("❌ Empty response")
            
    except requests.exceptions.Timeout:
        print("❌ Request timeout - API may be slow or unavailable")
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - check internet connection and API endpoint")
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_account_connection(api_key: str, username: str, password: str):
    """Test Instagram account connection via Unipile"""
    
    print(f"\n🔗 Testing Instagram account connection...")
    print(f"Username: {username}")
    
    base_url = "https://api8.unipile.com:13814/api/v1"
    headers = {
        "X-API-KEY": api_key,
        "accept": "application/json",
        "Content-Type": "application/json"
    }
    
    connect_data = {
        "provider": "INSTAGRAM",
        "username": username,
        "password": password
    }
    
    try:
        response = requests.post(f"{base_url}/accounts", headers=headers, json=connect_data, timeout=30)
        print(f"Status Code: {response.status_code}")
        
        if response.content:
            try:
                data = response.json()
                print(f"Response Data: {json.dumps(data, indent=2)}")
                
                if "error" in data:
                    print(f"❌ Connection failed: {data['error']}")
                elif data.get("object") == "Checkpoint":
                    checkpoint_type = data.get("checkpoint", {}).get("type", "Unknown")
                    print(f"🔐 Checkpoint required: {checkpoint_type}")
                    print(f"Account ID: {data.get('account_id')}")
                elif data.get("id"):
                    print(f"✅ Account connected successfully!")
                    print(f"Account ID: {data.get('id')}")
                    print(f"Username: @{data.get('username', 'N/A')}")
                    print(f"Name: {data.get('name', 'N/A')}")
                else:
                    print(f"❓ Unexpected response format")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                print(f"Raw response: {response.text}")
        else:
            print("❌ Empty response")
            
    except Exception as e:
        print(f"❌ Connection test failed: {e}")

def test_instagram_api_integration():
    """Test the Instagram API integration directly"""
    
    print(f"\n🧪 Testing Instagram API Integration...")
    
    try:
        # Import the Instagram API
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from instagram_api import InstagramMessaging
        
        # Initialize Instagram messaging
        instagram = InstagramMessaging()
        
        print(f"✅ Instagram API initialized successfully")
        print(f"Unipile client available: {bool(instagram.unipile_client)}")
        
        if instagram.unipile_client:
            print(f"API Key configured: {bool(instagram.unipile_client.api_key)}")
            
            # Test connection status
            status = instagram.get_connection_status()
            print(f"Connection status: {json.dumps(status, indent=2)}")
            
            # Test account info (if connected)
            account_info = instagram.get_account_info()
            print(f"Account info: {json.dumps(account_info, indent=2)}")
        
    except Exception as e:
        print(f"❌ Instagram API integration test failed: {e}")

if __name__ == "__main__":
    # Default API key from config
    api_key = "iGJsHDIR.sr6Pg30nB1cKUPC6eDY8tzVx+Opu+t+c6wQiKAEz2Ek="
    
    print("🧪 Instagram Unipile API Connection Test")
    print("=" * 50)
    
    # Test 1: Check existing accounts
    test_unipile_api(api_key)
    
    # Test 2: Instagram API integration
    test_instagram_api_integration()
    
    # Test 3: Account connection (optional)
    if len(sys.argv) >= 3:
        username = sys.argv[1]
        password = sys.argv[2]
        test_account_connection(api_key, username, password)
    else:
        print("\n💡 To test account connection, run:")
        print("python test_instagram_connection.py <username> <password>")
    
    print("\n✅ Test completed!")
