"""
Unified Messaging API Endpoints
FastAPI implementation with automatic OpenAPI documentation
Supports single messages, bulk campaigns, and platform management
Main API server for the unified messaging system
"""

from fastapi import FastAPI, HTTPException, BackgroundTasks, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any
import logging
from datetime import datetime
import sys
import os
import asyncio
import threading
import time

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from unified_messaging import UnifiedMessaging
from unipile_config import is_unipile_available

# Initialize FastAPI app
app = FastAPI(
    title="Unified Messaging API",
    description="Unified messaging system for all social media platforms using Unipile API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Mount static files for HTML authentication pages
try:
    # Get the current directory (integrations/)
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # Mount each platform's HTML files
    platforms = ["whatsapp", "telegram", "facebook", "instagram", "linkedin", "tiktok"]
    for platform in platforms:
        platform_dir = os.path.join(current_dir, f"{platform}_integration")
        if os.path.exists(platform_dir):
            app.mount(f"/{platform}", StaticFiles(directory=platform_dir, html=True), name=f"{platform}_static")
            logger.info(f"✅ Mounted {platform} static files from {platform_dir}")

    # Mount the main integrations directory for general files
    app.mount("/static", StaticFiles(directory=current_dir, html=True), name="static")

    logger.info("✅ Static file serving configured")
except Exception as e:
    logger.warning(f"⚠️  Static file serving setup warning: {e}")

# Initialize unified messaging system
try:
    unified_messaging = UnifiedMessaging()
    logger.info("✅ Unified messaging system initialized")
except Exception as e:
    logger.error(f"❌ Failed to initialize unified messaging: {e}")
    unified_messaging = None

# Background bot polling system
class BotPollingManager:
    """Manages background bot polling for automatic message responses with conflict prevention"""

    def __init__(self):
        self.is_running = False
        self.polling_task = None
        self.telegram_bot = None
        self.last_updates = []  # Cache recent updates
        self.last_update_time = None
        self.update_lock = threading.Lock()  # Prevent concurrent getUpdates calls
        self.stats = {
            "start_time": None,
            "messages_processed": 0,
            "responses_sent": 0,
            "errors": 0,
            "last_activity": None,
            "conflicts_prevented": 0
        }

    def start_polling(self, interval: float = 2.0):
        """Start background bot polling"""
        if self.is_running:
            return {"success": False, "error": "Bot polling is already running"}

        try:
            # Initialize Telegram bot
            from telegram_integration.telegram_api import TelegramMessaging
            self.telegram_bot = TelegramMessaging()

            # Test connection
            connection_test = self.telegram_bot.test_connection()
            if not connection_test.get("success"):
                return {
                    "success": False,
                    "error": f"Bot connection failed: {connection_test.get('error')}"
                }

            # Start background polling
            self.is_running = True
            self.stats["start_time"] = datetime.now()

            # Create background task
            def polling_worker():
                """Background worker for bot polling with update caching"""
                logger.info("🤖 Starting Telegram bot polling...")
                offset = None

                while self.is_running:
                    try:
                        # Get updates with lock to prevent conflicts
                        with self.update_lock:
                            updates_result = self.telegram_bot.get_updates(offset=offset, timeout=1)

                        if updates_result.get("ok") and updates_result.get("result"):
                            updates = updates_result["result"]

                            # Update cache even if no new messages
                            with self.update_lock:
                                if updates:
                                    # Add new updates to cache
                                    self.last_updates.extend(updates)
                                    # Keep only last 20 updates
                                    self.last_updates = self.last_updates[-20:]
                                self.last_update_time = datetime.now()

                            if updates:
                                logger.info(f"📨 Processing {len(updates)} new message(s)")

                                # Process updates and send responses
                                responses = self.telegram_bot.process_updates(updates)

                                # Update statistics
                                self.stats["messages_processed"] += len(updates)
                                successful_responses = sum(1 for r in responses if r.get("sent_successfully", False))
                                self.stats["responses_sent"] += successful_responses
                                self.stats["last_activity"] = datetime.now()

                                # Update offset to avoid processing same messages
                                offset = updates[-1]["update_id"] + 1

                                logger.info(f"✅ Processed {len(updates)} messages, sent {successful_responses} responses")

                        # Wait before next poll
                        time.sleep(interval)

                    except Exception as e:
                        error_msg = str(e)
                        if "409" in error_msg or "Conflict" in error_msg:
                            logger.warning("⚠️  Telegram API conflict detected - another bot instance may be running")
                            self.stats["errors"] += 1
                            time.sleep(interval * 3)  # Wait longer on conflict
                        else:
                            logger.error(f"❌ Bot polling error: {e}")
                            self.stats["errors"] += 1
                            time.sleep(interval * 2)  # Wait longer on error

                logger.info("🛑 Bot polling stopped")

            # Start polling in background thread
            import threading
            self.polling_task = threading.Thread(target=polling_worker, daemon=True)
            self.polling_task.start()

            return {
                "success": True,
                "message": "Bot polling started successfully",
                "bot_info": connection_test.get("bot_info", {}),
                "interval": interval
            }

        except Exception as e:
            logger.error(f"❌ Failed to start bot polling: {e}")
            self.is_running = False
            return {"success": False, "error": str(e)}

    def stop_polling(self):
        """Stop background bot polling"""
        if not self.is_running:
            return {"success": False, "error": "Bot polling is not running"}

        self.is_running = False

        # Wait for thread to finish
        if self.polling_task and self.polling_task.is_alive():
            self.polling_task.join(timeout=5)

        return {
            "success": True,
            "message": "Bot polling stopped successfully",
            "stats": self.get_stats()
        }

    def get_status(self):
        """Get current polling status"""
        return {
            "is_running": self.is_running,
            "bot_configured": self.telegram_bot is not None,
            "stats": self.get_stats(),
            "timestamp": datetime.now().isoformat()
        }

    def get_stats(self):
        """Get polling statistics"""
        stats = self.stats.copy()
        if stats["start_time"]:
            stats["start_time"] = stats["start_time"].isoformat()
        if stats["last_activity"]:
            stats["last_activity"] = stats["last_activity"].isoformat()
        return stats

    def get_cached_updates(self):
        """Get recent updates from cache (prevents API conflicts)"""
        with self.update_lock:
            return {
                "success": True,
                "data": {
                    "ok": True,
                    "result": self.last_updates.copy()
                },
                "cached": True,
                "last_update_time": self.last_update_time.isoformat() if self.last_update_time else None,
                "cache_size": len(self.last_updates)
            }

    def safe_get_updates(self, force=False):
        """Safely get updates without causing conflicts"""
        # If polling is running, return cached updates unless forced
        if self.is_running and not force:
            logger.info("📋 Returning cached updates (polling is active)")
            self.stats["conflicts_prevented"] += 1
            return self.get_cached_updates()

        # If not running or forced, try to get fresh updates
        with self.update_lock:
            try:
                if not self.telegram_bot:
                    from telegram_integration.telegram_api import TelegramMessaging
                    self.telegram_bot = TelegramMessaging()

                logger.info("📡 Getting fresh updates from Telegram API")
                result = self.telegram_bot.get_updates()

                if result.get("ok"):
                    # Update cache
                    updates = result.get("result", [])
                    self.last_updates = updates[-20:]  # Keep last 20 updates
                    self.last_update_time = datetime.now()

                    return {
                        "success": True,
                        "data": result,
                        "cached": False,
                        "fresh": True
                    }
                else:
                    return {
                        "success": False,
                        "error": result.get("description", "Failed to get updates"),
                        "telegram_error": result
                    }

            except Exception as e:
                logger.error(f"❌ Error getting updates: {e}")
                return {
                    "success": False,
                    "error": str(e)
                }

# Initialize bot polling manager
bot_polling_manager = BotPollingManager()
logger.info("✅ Bot polling manager initialized")

# Auto-start bot polling on server startup
@app.on_event("startup")
async def startup_event():
    """Auto-start bot polling when server starts"""
    try:
        # Check if auto-start is enabled (you can configure this)
        auto_start_bot = True  # Set to False if you don't want auto-start

        if auto_start_bot:
            logger.info("🚀 Auto-starting Telegram bot polling...")
            result = bot_polling_manager.start_polling(interval=2.0)

            if result["success"]:
                bot_info = result.get("bot_info", {})
                logger.info(f"✅ Bot polling auto-started! Bot: @{bot_info.get('username', 'unknown')}")
            else:
                logger.warning(f"⚠️  Failed to auto-start bot polling: {result['error']}")
        else:
            logger.info("ℹ️  Bot auto-start disabled. Use /api/telegram/polling to start manually.")

    except Exception as e:
        logger.error(f"❌ Error during startup: {e}")

@app.on_event("shutdown")
async def shutdown_event():
    """Stop bot polling when server shuts down"""
    try:
        if bot_polling_manager.is_running:
            logger.info("🛑 Stopping bot polling due to server shutdown...")
            result = bot_polling_manager.stop_polling()
            if result["success"]:
                logger.info("✅ Bot polling stopped successfully")
            else:
                logger.warning(f"⚠️  Error stopping bot polling: {result.get('error')}")
    except Exception as e:
        logger.error(f"❌ Error during shutdown: {e}")



# Pydantic models for request/response validation
class MessageRequest(BaseModel):
    platform: str = Field(..., description="Platform name (whatsapp, telegram, facebook, instagram, linkedin, tiktok)")
    recipient: str = Field(..., description="Recipient ID (phone number, username, etc.)")
    message: str = Field(..., description="Message content")
    options: Optional[Dict[str, Any]] = Field(default={}, description="Platform-specific options")

class RecipientData(BaseModel):
    contact: str = Field(..., description="Contact information")
    message: str = Field(..., description="Message content")

class BulkMessageRequest(BaseModel):
    campaign: Dict[str, List[RecipientData]] = Field(..., description="Campaign data organized by platform")
    delay: Optional[float] = Field(default=2.0, description="Delay between messages in seconds")

class SimpleBulkMessageRequest(BaseModel):
    platform: str = Field(..., description="Platform name (whatsapp, telegram, etc.)")
    recipients: List[str] = Field(..., description="List of phone numbers or usernames")
    message: str = Field(..., description="Message content")
    delay: Optional[float] = Field(default=2.0, description="Delay between messages in seconds")

class TemplateRecipient(BaseModel):
    platform: str = Field(..., description="Platform name")
    contact: str = Field(..., description="Contact information")
    name: Optional[str] = Field(default="", description="Recipient name")

class TemplateMessageRequest(BaseModel):
    template_name: str = Field(..., description="Template name")
    recipients: List[TemplateRecipient] = Field(..., description="List of recipients")
    variables: Optional[Dict[str, Any]] = Field(default={}, description="Template variables")

class MessageResponse(BaseModel):
    success: bool
    platform: str
    recipient: str
    message_id: Optional[str] = None
    method: Optional[str] = None
    error: Optional[str] = None
    timestamp: str

class HealthResponse(BaseModel):
    success: bool
    status: str
    unified_messaging: bool
    unipile_available: bool
    timestamp: str

class PlatformStatusResponse(BaseModel):
    success: bool
    platform: Optional[str] = None
    status: Optional[Dict[str, Any]] = None
    platforms: Optional[Dict[str, Any]] = None
    timestamp: str

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with platform links"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Unified Messaging System</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
            .platform { display: inline-block; margin: 10px; padding: 20px; border: 1px solid #ddd; border-radius: 10px; text-decoration: none; color: #333; }
            .platform:hover { background-color: #f5f5f5; }
            h1 { color: #333; text-align: center; }
        </style>
    </head>
    <body>
        <h1>🌟 Unified Messaging System</h1>
        <p>Choose a platform to configure:</p>
        <div>
            <a href="/telegram/telegram_auth.html" class="platform">
                <h3>📱 Telegram</h3>
                <p>Connect your Telegram account with QR code</p>
            </a>
            <a href="/telegram/bot_tester.html" class="platform" style="background: linear-gradient(135deg, #0088cc, #28a745);">
                <h3>🤖 Telegram Bot Tester</h3>
                <p>Test bot responses & monitor messages</p>
            </a>
            <a href="/whatsapp/whatsapp_auth.html" class="platform">
                <h3>💬 WhatsApp</h3>
                <p>Configure WhatsApp messaging</p>
            </a>
            <a href="/whatsapp/whatsapp_test.html" class="platform" style="background: linear-gradient(135deg, #25D366, #128C7E);">
                <h3>📱 WhatsApp Test</h3>
                <p>Test WhatsApp messaging functionality</p>
            </a>
            <a href="/whatsapp_messaging_guide.html" class="platform" style="background: linear-gradient(135deg, #128C7E, #075E54);">
                <h3>📋 WhatsApp Guide</h3>
                <p>Step-by-step messaging guide</p>
            </a>
            <a href="/facebook/facebook_auth.html" class="platform">
                <h3>📘 Facebook</h3>
                <p>Configure Facebook Messenger</p>
            </a>
            <a href="/instagram/instagram_auth.html" class="platform">
                <h3>📷 Instagram</h3>
                <p>Configure Instagram messaging</p>
            </a>
            <a href="/linkedin/linkedin_auth.html" class="platform">
                <h3>💼 LinkedIn</h3>
                <p>Configure LinkedIn messaging</p>
            </a>
            <a href="/tiktok/tiktok_auth.html" class="platform">
                <h3>🎵 TikTok</h3>
                <p>Configure TikTok messaging</p>
            </a>
        </div>
        <hr>
        <p><a href="/docs">📖 API Documentation</a> | <a href="/api/health">🔍 Health Check</a></p>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

# Health check endpoint
@app.get("/api/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(
        success=True,
        status="healthy",
        unified_messaging=unified_messaging is not None,
        unipile_available=is_unipile_available(),
        timestamp=datetime.now().isoformat()
    )

# Single message sending endpoint
@app.post("/api/messaging/send", response_model=MessageResponse)
async def send_message(request: MessageRequest):
    """
    Send single message to specified platform
    
    - **platform**: Platform name (whatsapp, telegram, facebook, instagram, linkedin, tiktok)
    - **recipient**: Recipient ID (phone number, username, etc.)
    - **message**: Message content
    - **options**: Platform-specific options (optional)
    """
    if not unified_messaging:
        raise HTTPException(status_code=503, detail="Unified messaging system not available")

    try:
        # Send message
        result = unified_messaging.send_message_by_platform(
            request.platform, 
            request.recipient, 
            request.message, 
            **request.options
        )

        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)

        return MessageResponse(
            success=result.success,
            platform=result.platform,
            recipient=result.recipient,
            message_id=result.message_id,
            method=result.method,
            error=result.error,
            timestamp=result.timestamp
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in send_message: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Bulk message sending endpoint
@app.post("/api/messaging/bulk")
async def send_bulk_messages(request: BulkMessageRequest):
    """
    Send bulk messages across multiple platforms
    
    - **campaign**: Dictionary with platform names as keys and lists of recipient data as values
    - **delay**: Delay between messages in seconds (default: 2.0)
    """
    if not unified_messaging:
        raise HTTPException(status_code=503, detail="Unified messaging system not available")

    try:
        # Convert Pydantic models to dictionaries
        campaign_data = {}
        for platform, recipients in request.campaign.items():
            campaign_data[platform] = [
                {"contact": r.contact, "message": r.message}
                for r in recipients
            ]

        # Send bulk campaign
        results = unified_messaging.send_bulk_campaign(campaign_data, request.delay)

        # Calculate summary
        total_messages = sum(len(platform_results) for platform_results in results.values())
        successful_messages = sum(
            1 for platform_results in results.values() 
            for result in platform_results if result.success
        )

        # Format response
        response = {
            "success": True,
            "campaign_summary": {
                "total_messages": total_messages,
                "successful": successful_messages,
                "failed": total_messages - successful_messages,
                "success_rate": (successful_messages / total_messages * 100) if total_messages > 0 else 0
            },
            "results": {},
            "timestamp": datetime.now().isoformat()
        }

        # Add detailed results
        for platform, platform_results in results.items():
            response["results"][platform] = [
                {
                    "success": result.success,
                    "recipient": result.recipient,
                    "message_id": result.message_id,
                    "error": result.error,
                    "method": result.method,
                    "timestamp": result.timestamp
                }
                for result in platform_results
            ]

        return response

    except Exception as e:
        logger.error(f"Error in send_bulk_messages: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Simple bulk message sending endpoint (just phone numbers)
@app.post("/api/messaging/bulk-simple")
async def send_simple_bulk_messages(request: SimpleBulkMessageRequest):
    """
    Send bulk messages to a list of phone numbers/usernames

    - **platform**: Platform name (whatsapp, telegram, facebook, instagram, linkedin, tiktok)
    - **recipients**: List of phone numbers or usernames
    - **message**: Message content
    - **delay**: Delay between messages in seconds (default: 2.0)
    """
    if not unified_messaging:
        raise HTTPException(status_code=503, detail="Unified messaging system not available")

    try:
        # Send messages to all recipients
        results = []
        for recipient in request.recipients:
            result = unified_messaging.send_message_by_platform(
                request.platform,
                recipient,
                request.message
            )
            results.append(result)

            # Add delay between messages
            if request.delay > 0:
                import time
                time.sleep(request.delay)

        # Calculate summary
        total = len(results)
        successful = sum(1 for r in results if r.success)

        return {
            "success": True,
            "platform": request.platform,
            "summary": {
                "total_messages": total,
                "successful": successful,
                "failed": total - successful,
                "success_rate": (successful / total * 100) if total > 0 else 0
            },
            "results": [
                {
                    "success": result.success,
                    "recipient": result.recipient,
                    "message_id": result.message_id,
                    "error": result.error,
                    "method": result.method,
                    "timestamp": result.timestamp
                }
                for result in results
            ],
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error in send_simple_bulk_messages: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Platform status endpoint
@app.get("/api/messaging/status/{platform}", response_model=PlatformStatusResponse)
async def get_platform_status(platform: str):
    """
    Check if platform is connected and ready
    
    - **platform**: Platform name or 'all' for all platforms
    """
    if not unified_messaging:
        raise HTTPException(status_code=503, detail="Unified messaging system not available")

    try:
        # Get all platform status
        all_status = unified_messaging.get_platform_status()

        if platform.lower() == "all":
            return PlatformStatusResponse(
                success=True,
                platforms=all_status,
                timestamp=datetime.now().isoformat()
            )

        # Get specific platform status
        platform_status = all_status.get(platform.lower())
        if platform_status is None:
            raise HTTPException(status_code=404, detail=f"Platform '{platform}' not supported")

        return PlatformStatusResponse(
            success=True,
            platform=platform.lower(),
            status=platform_status,
            timestamp=datetime.now().isoformat()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_platform_status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Get all platforms status
@app.get("/api/messaging/status", response_model=PlatformStatusResponse)
async def get_all_platforms_status():
    """Get status of all platforms"""
    return await get_platform_status("all")

# Message analytics endpoint
@app.get("/api/messaging/analytics")
async def get_message_analytics():
    """Get message analytics and statistics"""
    if not unified_messaging:
        raise HTTPException(status_code=503, detail="Unified messaging system not available")

    try:
        analytics = unified_messaging.get_message_analytics()
        
        return {
            "success": True,
            "analytics": analytics,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error in get_message_analytics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Template message endpoint
@app.post("/api/messaging/template")
async def send_template_message(request: TemplateMessageRequest):
    """
    Send template message to multiple recipients
    
    - **template_name**: Name of the template to use
    - **recipients**: List of recipients with platform and contact info
    - **variables**: Variables to substitute in the template
    """
    if not unified_messaging:
        raise HTTPException(status_code=503, detail="Unified messaging system not available")

    try:
        # Load template from config
        config = unified_messaging.config
        templates = config.get("message_templates", {})
        
        if request.template_name not in templates:
            raise HTTPException(status_code=404, detail=f"Template '{request.template_name}' not found")

        template_data = templates[request.template_name]
        message_template = template_data.get("text", template_data) if isinstance(template_data, dict) else template_data

        # Send messages to recipients
        results = []
        for recipient in request.recipients:
            # Format message with variables and recipient data
            message_vars = {**request.variables, **recipient.model_dump()}
            try:
                message = message_template.format(**message_vars)
            except KeyError as e:
                results.append({
                    "success": False,
                    "error": f"Missing template variable: {e}",
                    "recipient": recipient.model_dump()
                })
                continue

            # Send message
            result = unified_messaging.send_message_by_platform(
                recipient.platform, 
                recipient.contact, 
                message
            )
            results.append({
                "success": result.success,
                "platform": result.platform,
                "recipient": result.recipient,
                "message_id": result.message_id,
                "error": result.error,
                "method": result.method,
                "timestamp": result.timestamp
            })

        # Calculate summary
        total = len(results)
        successful = sum(1 for r in results if r.get("success"))

        return {
            "success": True,
            "template_name": request.template_name,
            "summary": {
                "total": total,
                "successful": successful,
                "failed": total - successful
            },
            "results": results,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in send_template_message: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Configuration endpoints
@app.get("/api/config/templates")
async def get_templates():
    """Get available message templates"""
    if not unified_messaging:
        raise HTTPException(status_code=503, detail="Unified messaging system not available")

    try:
        config = unified_messaging.config
        templates = config.get("message_templates", {})

        return {
            "success": True,
            "templates": templates,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error in get_templates: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/config/platforms")
async def get_supported_platforms():
    """Get list of supported platforms"""
    return {
        "success": True,
        "platforms": [
            {
                "name": "whatsapp",
                "display_name": "WhatsApp",
                "supports_media": True,
                "auth_method": "qr_code"
            },
            {
                "name": "telegram",
                "display_name": "Telegram",
                "supports_media": True,
                "auth_method": "bot_token"
            },
            {
                "name": "facebook",
                "display_name": "Facebook Messenger",
                "supports_media": True,
                "auth_method": "oauth"
            },
            {
                "name": "instagram",
                "display_name": "Instagram",
                "supports_media": True,
                "auth_method": "oauth"
            },
            {
                "name": "linkedin",
                "display_name": "LinkedIn",
                "supports_media": False,
                "auth_method": "oauth"
            },
            {
                "name": "tiktok",
                "display_name": "TikTok",
                "supports_media": False,
                "auth_method": "oauth"
            }
        ],
        "timestamp": datetime.now().isoformat()
    }

# Platform-specific configuration endpoints
@app.post("/api/{platform}/config")
async def update_platform_config(platform: str, config: Dict[str, Any]):
    """Update platform-specific configuration"""
    if platform not in ["whatsapp", "telegram", "facebook", "instagram", "linkedin", "tiktok"]:
        raise HTTPException(status_code=404, detail=f"Platform '{platform}' not supported")

    try:
        # Import and update platform-specific configuration
        if platform == "whatsapp":
            from whatsapp_integration.whatsapp_api import WhatsAppMessaging
            api = WhatsAppMessaging()
            result = api.update_config(**config)
        elif platform == "telegram":
            from telegram_integration.telegram_api import TelegramMessaging
            api = TelegramMessaging()
            result = api.update_config(**config)
        elif platform == "facebook":
            from facebook_integration.facebook_api import FacebookMessaging
            api = FacebookMessaging()
            result = api.update_config(**config)
        elif platform == "instagram":
            from instagram_integration.instagram_api import InstagramMessaging
            api = InstagramMessaging()
            result = api.update_config(**config)
        elif platform == "linkedin":
            from linkedin_integration.linkedin_api import LinkedInMessaging
            api = LinkedInMessaging()
            result = api.update_config(**config)
        elif platform == "tiktok":
            from tiktok_integration.tiktok_api import TikTokMessaging
            api = TikTokMessaging()
            result = api.update_config(**config)

        return {
            "success": True,
            "platform": platform,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error updating {platform} config: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/{platform}/test")
async def test_platform_connection(platform: str):
    """Test platform connection"""
    if platform not in ["whatsapp", "telegram", "facebook", "instagram", "linkedin", "tiktok"]:
        raise HTTPException(status_code=404, detail=f"Platform '{platform}' not supported")

    try:
        # Test connection based on platform
        if platform == "whatsapp":
            from whatsapp_integration.whatsapp_api import WhatsAppMessaging
            api = WhatsAppMessaging()
            result = api.test_connection()
        elif platform == "telegram":
            from telegram_integration.telegram_api import TelegramMessaging
            api = TelegramMessaging()
            result = api.test_connection()
        elif platform == "facebook":
            from facebook_integration.facebook_api import FacebookMessaging
            api = FacebookMessaging()
            result = api.test_connection()
        elif platform == "instagram":
            from instagram_integration.instagram_api import InstagramMessaging
            api = InstagramMessaging()
            result = api.test_connection()
        elif platform == "linkedin":
            from linkedin_integration.linkedin_api import LinkedInMessaging
            api = LinkedInMessaging()
            result = api.test_connection()
        elif platform == "tiktok":
            from tiktok_integration.tiktok_api import TikTokMessaging
            api = TikTokMessaging()
            result = api.test_connection()

        return {
            "success": True,
            "platform": platform,
            "test_result": result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error testing {platform} connection: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Facebook-specific endpoints
class FacebookConfigRequest(BaseModel):
    page_access_token: str = Field(..., description="Facebook Page Access Token")
    app_secret: str = Field(..., description="Facebook App Secret")
    page_id: str = Field(..., description="Facebook Page ID")

@app.post("/api/facebook/config")
async def update_facebook_config(request: FacebookConfigRequest):
    """Update Facebook configuration"""
    try:
        from facebook_integration.facebook_api import FacebookMessaging

        facebook = FacebookMessaging()

        # Update configuration
        facebook.config.update({
            "page_access_token": request.page_access_token,
            "app_secret": request.app_secret,
            "page_id": request.page_id
        })

        facebook._save_config()

        return {
            "success": True,
            "message": "Facebook configuration updated successfully",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error updating Facebook config: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/facebook/status")
async def get_facebook_status():
    """Get Facebook connection status"""
    try:
        from facebook_integration.facebook_api import FacebookMessaging

        facebook = FacebookMessaging()

        # Check if configured
        if not facebook.page_access_token:
            return {
                "success": True,
                "connected": False,
                "message": "Facebook not configured",
                "timestamp": datetime.now().isoformat()
            }

        # Test connection
        test_result = facebook.test_connection()

        if test_result.get("success"):
            return {
                "success": True,
                "connected": True,
                "page_name": test_result.get("page_name", "Unknown"),
                "page_id": facebook.page_id,
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": True,
                "connected": False,
                "error": test_result.get("error", "Connection test failed"),
                "timestamp": datetime.now().isoformat()
            }

    except Exception as e:
        logger.error(f"Error getting Facebook status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/facebook/oauth/start")
async def start_facebook_oauth():
    """Start Facebook OAuth flow"""
    try:
        from facebook_integration.facebook_api import FacebookMessaging

        facebook = FacebookMessaging()
        result = facebook.start_oauth_flow()

        if result.get("success"):
            return {
                "success": True,
                "auth_url": result["auth_url"],
                "state": result["state"],
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=400, detail=result.get("error", "Failed to start OAuth flow"))

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting Facebook OAuth: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/facebook/oauth/callback")
async def facebook_oauth_callback(code: str, state: str):
    """Handle Facebook OAuth callback"""
    try:
        from facebook_integration.facebook_api import FacebookMessaging

        facebook = FacebookMessaging()
        result = facebook.handle_oauth_callback(code, state)

        if result.get("success"):
            # Store completion status for polling
            oauth_states[state] = {
                "completed": True,
                "success": True,
                "page_name": result.get("page_name", "Connected"),
                "page_id": result.get("page_id", "N/A"),
                "timestamp": datetime.now().isoformat()
            }

            return {
                "success": True,
                "message": "OAuth completed successfully",
                "redirect_url": "/facebook/facebook_auth.html"
            }
        else:
            oauth_states[state] = {
                "completed": True,
                "success": False,
                "error": result.get("error", "OAuth failed"),
                "timestamp": datetime.now().isoformat()
            }
            raise HTTPException(status_code=400, detail=result.get("error", "OAuth callback failed"))

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in Facebook OAuth callback: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Global storage for OAuth states (in production, use Redis or database)
oauth_states = {}

@app.get("/api/facebook/oauth/status")
async def get_facebook_oauth_status(state: str):
    """Get Facebook OAuth status"""
    try:
        if state in oauth_states:
            return oauth_states[state]
        else:
            return {
                "completed": False,
                "message": "OAuth in progress",
                "timestamp": datetime.now().isoformat()
            }

    except Exception as e:
        logger.error(f"Error getting Facebook OAuth status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/facebook/test")
async def test_facebook_connection():
    """Test Facebook connection"""
    try:
        from facebook_integration.facebook_api import FacebookMessaging

        facebook = FacebookMessaging()
        result = facebook.test_connection()

        if result.get("success"):
            return {
                "success": True,
                "page_name": result.get("page_name"),
                "page_id": result.get("page_id"),
                "category": result.get("category"),
                "message": result.get("message"),
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": False,
                "error": result.get("error"),
                "timestamp": datetime.now().isoformat()
            }

    except Exception as e:
        logger.error(f"Error testing Facebook connection: {e}")
        raise HTTPException(status_code=500, detail=str(e))

class FacebookGreetingRequest(BaseModel):
    greeting: str = Field(..., description="Greeting text for the Facebook page")

@app.post("/api/facebook/greeting")
async def set_facebook_greeting(request: FacebookGreetingRequest):
    """Set Facebook page greeting text"""
    try:
        from facebook_integration.facebook_api import FacebookMessaging

        facebook = FacebookMessaging()
        result = facebook.set_page_settings(greeting_text=request.greeting)

        if result.get("success"):
            return {
                "success": True,
                "message": "Greeting text set successfully",
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": False,
                "error": result.get("error", "Failed to set greeting"),
                "timestamp": datetime.now().isoformat()
            }

    except Exception as e:
        logger.error(f"Error setting Facebook greeting: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/facebook/get-started")
async def set_facebook_get_started():
    """Set Facebook page get started button"""
    try:
        from facebook_integration.facebook_api import FacebookMessaging

        facebook = FacebookMessaging()
        result = facebook.set_page_settings(get_started_payload="GET_STARTED")

        if result.get("success"):
            return {
                "success": True,
                "message": "Get Started button configured successfully",
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": False,
                "error": result.get("error", "Failed to set Get Started button"),
                "timestamp": datetime.now().isoformat()
            }

    except Exception as e:
        logger.error(f"Error setting Facebook Get Started button: {e}")
        raise HTTPException(status_code=500, detail=str(e))

class FacebookTestMessageRequest(BaseModel):
    recipient_id: str = Field(..., description="Facebook Page-Scoped ID (PSID) of the recipient")
    message: str = Field(..., description="Test message to send")

@app.post("/api/facebook/send-test")
async def send_facebook_test_message(request: FacebookTestMessageRequest):
    """Send a test message via Facebook Messenger"""
    try:
        from facebook_integration.facebook_api import FacebookMessaging

        facebook = FacebookMessaging()
        result = facebook.send_message(request.recipient_id, request.message)

        if result.get("success"):
            return {
                "success": True,
                "message_id": result.get("message_id"),
                "recipient_id": request.recipient_id,
                "message": "Test message sent successfully",
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": False,
                "error": result.get("error", "Failed to send test message"),
                "timestamp": datetime.now().isoformat()
            }

    except Exception as e:
        logger.error(f"Error sending Facebook test message: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Unipile-specific endpoints
class UnipileConnectRequest(BaseModel):
    api_key: str = Field(..., description="Unipile API key")

@app.post("/api/unipile/connect")
async def connect_unipile(request: UnipileConnectRequest):
    """Connect to Unipile API"""
    try:
        # Update Unipile configuration
        from unipile_config import UnipileConfig
        config = UnipileConfig()
        config.api_key = request.api_key
        config.save_config()

        # Test connection
        test_result = config.test_connection()

        if not test_result.get("success", False):
            raise HTTPException(status_code=400, detail=test_result.get("error", "Connection failed"))

        return {
            "success": True,
            "message": test_result.get("message", "Connected successfully"),
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error connecting to Unipile: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/unipile/accounts")
async def get_unipile_accounts():
    """Get Unipile connected accounts"""
    try:
        from unipile_config import get_unipile_client
        client = get_unipile_client()
        accounts = client.get_accounts()

        return {
            "success": True,
            "accounts": accounts,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting Unipile accounts: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# LinkedIn-specific endpoints for InMail and professional messaging
class LinkedInInMailRequest(BaseModel):
    recipient_id: str = Field(..., description="LinkedIn person ID")
    subject: str = Field(..., description="InMail subject")
    message_body: str = Field(..., description="InMail message content")

@app.post("/api/linkedin/send-inmail")
async def send_linkedin_inmail(request: LinkedInInMailRequest):
    """Send LinkedIn InMail"""
    try:
        from linkedin_integration.linkedin_api import LinkedInMessaging
        linkedin = LinkedInMessaging()

        result = linkedin.send_inmail(
            request.recipient_id,
            request.subject,
            request.message_body
        )

        if not result.get("success", False):
            raise HTTPException(status_code=400, detail=result.get("error", "Failed to send InMail"))

        return {
            "success": True,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending LinkedIn InMail: {e}")
        raise HTTPException(status_code=500, detail=str(e))

class LinkedInConnectionMessageRequest(BaseModel):
    recipient_id: str = Field(..., description="LinkedIn person ID")
    message: str = Field(..., description="Connection message content")

@app.post("/api/linkedin/send-connection-message")
async def send_linkedin_connection_message(request: LinkedInConnectionMessageRequest):
    """Send LinkedIn connection message with enhanced error handling"""
    try:
        from linkedin_integration.linkedin_api import LinkedInMessaging
        linkedin = LinkedInMessaging()

        result = linkedin.send_connection_message(
            request.recipient_id,
            request.message
        )

        if result.get("success", False):
            return {
                "success": True,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
        else:
            # Return enhanced error response instead of raising HTTPException
            error_response = {
                "success": False,
                "error": result.get("error", "Failed to send connection message"),
                "error_type": result.get("error_type", "unknown"),
                "reason": result.get("reason"),
                "solutions": result.get("solutions", []),
                "suggestion": result.get("suggestion"),
                "retry_recommended": result.get("retry_recommended", False),
                "retry_delay_seconds": result.get("retry_delay_seconds"),
                "fallback_available": result.get("fallback_available", True),
                "identifier_type": result.get("identifier_type"),
                "identifier_tested": result.get("identifier_tested"),
                "attempted_recipient": result.get("attempted_recipient"),
                "timestamp": datetime.now().isoformat()
            }

            # Remove None values for cleaner response
            error_response = {k: v for k, v in error_response.items() if v is not None}

            # Return appropriate HTTP status based on error type
            if result.get("error_type") == "server_error":
                return JSONResponse(status_code=503, content=error_response)  # Service Unavailable
            elif result.get("error_type") == "validation_error":
                return JSONResponse(status_code=422, content=error_response)  # Unprocessable Entity
            elif result.get("error_type") == "auth_error":
                return JSONResponse(status_code=401, content=error_response)  # Unauthorized
            else:
                return JSONResponse(status_code=400, content=error_response)  # Bad Request

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending LinkedIn connection message: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# LinkedIn Unipile integration endpoints
class LinkedInUnipileConnectRequest(BaseModel):
    api_key: str = Field(..., description="Unipile API key")

@app.post("/api/linkedin/unipile/connect")
async def connect_linkedin_unipile(request: LinkedInUnipileConnectRequest):
    """Connect LinkedIn via Unipile API"""
    try:
        from linkedin_integration.linkedin_api import LinkedInMessaging

        # Test the API key by creating a LinkedIn instance with it
        linkedin = LinkedInMessaging()
        # Update the Unipile client with the new API key
        linkedin.unipile_client.api_key = request.api_key
        linkedin.unipile_client.headers["X-API-KEY"] = request.api_key

        # Test the connection
        accounts = linkedin.unipile_client.get_accounts()

        if "error" in accounts:
            raise HTTPException(status_code=400, detail=f"Failed to connect to Unipile: {accounts['error']}")

        # Save the API key to config for future use
        linkedin.config["unipile_api_key"] = request.api_key
        linkedin.config.setdefault("unipile", {})["api_key"] = request.api_key
        linkedin._save_config()

        # Handle both response formats
        all_accounts = accounts.get("items", accounts.get("accounts", []))
        linkedin_accounts = [acc for acc in all_accounts if acc.get("type") == "LINKEDIN"]

        return {
            "success": True,
            "message": "Unipile API connected successfully",
            "accounts_found": len(all_accounts),
            "linkedin_accounts_found": len(linkedin_accounts),
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error connecting LinkedIn via Unipile: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/linkedin/unipile/status")
async def get_linkedin_unipile_status():
    """Get LinkedIn Unipile connection status and accounts"""
    try:
        from linkedin_integration.linkedin_api import LinkedInMessaging

        linkedin = LinkedInMessaging()

        # Make sure we're using the latest API key from config
        if not linkedin.unipile_client:
            raise HTTPException(status_code=400, detail="Unipile client not available")

        accounts = linkedin.unipile_client.get_accounts()

        if "error" in accounts:
            # Provide more detailed error information
            error_detail = accounts.get("error", "Unknown error")
            logger.error(f"Unipile API error: {error_detail}")
            raise HTTPException(status_code=400, detail=f"Failed to get Unipile status: {error_detail}")

        # Filter for LinkedIn accounts - handle both response formats
        all_accounts = accounts.get("items", accounts.get("accounts", []))
        linkedin_accounts = [acc for acc in all_accounts if acc.get("type") == "LINKEDIN"]

        # Add debug information
        logger.info(f"Unipile status check: {len(all_accounts)} total accounts, {len(linkedin_accounts)} LinkedIn accounts")

        return {
            "success": True,
            "connected": len(linkedin_accounts) > 0,
            "accounts": linkedin_accounts,
            "total_accounts": len(all_accounts),
            "linkedin_accounts": len(linkedin_accounts),
            "debug_info": {
                "api_key_configured": bool(linkedin.unipile_client.api_key),
                "response_format": "items" if "items" in accounts else "accounts" if "accounts" in accounts else "unknown"
            },
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting LinkedIn Unipile status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# LinkedIn configuration and profile endpoints
class LinkedInConfigRequest(BaseModel):
    client_id: Optional[str] = Field(None, description="LinkedIn app client ID")
    client_secret: Optional[str] = Field(None, description="LinkedIn app client secret")
    access_token: Optional[str] = Field(None, description="LinkedIn access token")
    refresh_token: Optional[str] = Field(None, description="LinkedIn refresh token")

@app.post("/api/linkedin/config")
async def update_linkedin_config(request: LinkedInConfigRequest):
    """Update LinkedIn API configuration"""
    try:
        from linkedin_integration.linkedin_api import LinkedInMessaging
        linkedin = LinkedInMessaging()

        config_data = {k: v for k, v in request.model_dump().items() if v is not None}
        result = linkedin.update_config(**config_data)

        if not result.get("success", False):
            raise HTTPException(status_code=400, detail=result.get("error", "Configuration update failed"))

        return {
            "success": True,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating LinkedIn config: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/linkedin/profile")
async def get_linkedin_profile():
    """Get LinkedIn profile information"""
    try:
        from linkedin_integration.linkedin_api import LinkedInMessaging
        linkedin = LinkedInMessaging()

        result = linkedin.get_profile()

        if "error" in result:
            raise HTTPException(status_code=400, detail=result.get("error", "Failed to get profile"))

        return {
            "success": True,
            "data": result,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting LinkedIn profile: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/linkedin/connections")
async def get_linkedin_connections():
    """Get LinkedIn connections"""
    try:
        from linkedin_integration.linkedin_api import LinkedInMessaging
        linkedin = LinkedInMessaging()

        result = linkedin.get_connections()

        if "error" in result:
            raise HTTPException(status_code=400, detail=result.get("error", "Failed to get connections"))

        return {
            "success": True,
            "data": result,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting LinkedIn connections: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/linkedin/status")
async def get_linkedin_status():
    """Get LinkedIn connection status"""
    try:
        from linkedin_integration.linkedin_api import LinkedInMessaging
        linkedin = LinkedInMessaging()

        result = linkedin.get_connection_status()

        return {
            "success": True,
            "status": result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting LinkedIn status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# LinkedIn debug endpoint
class LinkedInDebugRequest(BaseModel):
    recipient_id: str = Field(..., description="LinkedIn identifier to debug")
    account_id: Optional[str] = Field(None, description="Optional account ID")

@app.post("/api/linkedin/debug")
async def debug_linkedin_connection(request: LinkedInDebugRequest):
    """Debug LinkedIn connection issues"""
    try:
        from linkedin_integration.linkedin_api import LinkedInMessaging
        linkedin = LinkedInMessaging()

        result = linkedin.debug_connection_issue(
            recipient_id=request.recipient_id,
            account_id=request.account_id
        )

        return {
            "success": True,
            "debug_info": result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error debugging LinkedIn connection: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# LinkedIn company page messaging endpoint
class LinkedInCompanyMessageRequest(BaseModel):
    company_id: str = Field(..., description="LinkedIn company ID")
    recipient_id: str = Field(..., description="Recipient LinkedIn ID")
    message: str = Field(..., description="Message content")

@app.post("/api/linkedin/send-company-message")
async def send_linkedin_company_message(request: LinkedInCompanyMessageRequest):
    """Send message from LinkedIn company page"""
    try:
        from linkedin_integration.linkedin_api import LinkedInMessaging
        linkedin = LinkedInMessaging()

        result = linkedin.send_company_page_message(
            company_id=request.company_id,
            recipient_id=request.recipient_id,
            message=request.message
        )

        if result.get("success"):
            return {
                "success": True,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=400, detail=result.get("error", "Failed to send company message"))

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending LinkedIn company message: {e}")
        raise HTTPException(status_code=500, detail=str(e))



# LinkedIn Direct Connection endpoint
class LinkedInDirectConnectRequest(BaseModel):
    username: str = Field(..., description="LinkedIn username/email")
    password: str = Field(..., description="LinkedIn password")

@app.post("/api/linkedin/connect-account")
async def connect_linkedin_account_direct(request: LinkedInDirectConnectRequest):
    """Connect LinkedIn account directly using credentials"""
    try:
        from linkedin_integration.linkedin_api import LinkedInMessaging

        linkedin = LinkedInMessaging()
        if not linkedin.unipile_client:
            raise HTTPException(status_code=400, detail="Unipile client not available")

        # Connect LinkedIn account using Unipile API
        connect_data = {
            "provider": "LINKEDIN",
            "username": request.username,
            "password": request.password
        }

        logger.info(f"Attempting to connect LinkedIn account for username: {request.username}")
        result = linkedin.unipile_client.make_request("POST", "accounts", connect_data)
        logger.info(f"Unipile connection response: {result}")

        # Handle different response types
        if "error" in result:
            error_msg = result.get("error", "Unknown error")
            logger.error(f"LinkedIn connection failed: {error_msg}")
            raise HTTPException(status_code=400, detail=f"Failed to connect LinkedIn account: {error_msg}")

        # Check if this is a checkpoint response (status 202)
        if result.get("object") == "Checkpoint":
            checkpoint_type = result.get("checkpoint", {}).get("type", "Unknown")
            account_id = result.get("account_id")
            logger.info(f"Checkpoint required: {checkpoint_type} for account {account_id}")

            return {
                "success": False,
                "checkpoint_required": True,
                "checkpoint_type": checkpoint_type,
                "account_id": account_id,
                "message": f"LinkedIn authentication requires {checkpoint_type} verification",
                "instructions": f"Please solve the {checkpoint_type} checkpoint to complete authentication",
                "timestamp": datetime.now().isoformat()
            }

        # Successful connection
        if result.get("id"):
            account_id = result.get("id")
            logger.info(f"LinkedIn account connected successfully with ID: {account_id}")

            # Clear the cached account ID so it gets refreshed
            linkedin._linkedin_account_id = None

            return {
                "success": True,
                "account_id": account_id,
                "message": "LinkedIn account connected successfully",
                "debug_info": {
                    "account_name": result.get("name"),
                    "account_email": result.get("email"),
                    "provider": result.get("provider")
                },
                "timestamp": datetime.now().isoformat()
            }
        else:
            # Unexpected response format
            logger.error(f"Unexpected Unipile response format: {result}")
            raise HTTPException(status_code=400, detail=f"Unexpected response from Unipile: {result}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error connecting LinkedIn account: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# LinkedIn Checkpoint Solving endpoint
class LinkedInCheckpointRequest(BaseModel):
    account_id: str = Field(..., description="Account ID from checkpoint response")
    code: str = Field(..., description="Verification code (2FA, OTP, or phone number)")

@app.post("/api/linkedin/solve-checkpoint")
async def solve_linkedin_checkpoint(request: LinkedInCheckpointRequest):
    """Solve LinkedIn authentication checkpoint (2FA, OTP, etc.)"""
    try:
        from linkedin_integration.linkedin_api import LinkedInMessaging

        linkedin = LinkedInMessaging()
        if not linkedin.unipile_client:
            raise HTTPException(status_code=400, detail="Unipile client not available")

        # Solve checkpoint using Unipile API
        checkpoint_data = {
            "provider": "LINKEDIN",
            "account_id": request.account_id,
            "code": request.code
        }

        result = linkedin.unipile_client.make_request("POST", "accounts/checkpoint", checkpoint_data)

        # Handle different response types
        if "error" in result:
            raise HTTPException(status_code=400, detail=f"Failed to solve checkpoint: {result['error']}")

        # Check if another checkpoint is required
        if result.get("object") == "Checkpoint":
            checkpoint_type = result.get("checkpoint", {}).get("type", "Unknown")

            return {
                "success": False,
                "checkpoint_required": True,
                "checkpoint_type": checkpoint_type,
                "account_id": request.account_id,
                "message": f"Additional {checkpoint_type} verification required",
                "timestamp": datetime.now().isoformat()
            }

        # Successful authentication
        if result.get("id"):
            # Clear the cached account ID so it gets refreshed
            linkedin._linkedin_account_id = None

            return {
                "success": True,
                "account_id": result.get("id"),
                "message": "LinkedIn account connected successfully after checkpoint verification",
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": True,
                "message": "Checkpoint solved successfully",
                "result": result,
                "timestamp": datetime.now().isoformat()
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error solving LinkedIn checkpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Instagram-specific endpoints
class InstagramDirectConnectRequest(BaseModel):
    username: str = Field(..., description="Instagram username/email")
    password: str = Field(..., description="Instagram password")

@app.post("/api/instagram/connect-account")
async def connect_instagram_account_direct(request: InstagramDirectConnectRequest):
    """Connect Instagram account directly using credentials"""
    try:
        from instagram_integration.instagram_api import InstagramMessaging

        instagram = InstagramMessaging()
        if not instagram.unipile_client:
            raise HTTPException(status_code=400, detail="Unipile client not available")

        # Use the improved connect_account method
        result = instagram.connect_account(request.username, request.password)

        # Handle different response types
        if result.get("success"):
            return {
                "success": True,
                "account_id": result.get("account_id"),
                "message": result.get("message", "Instagram account connected successfully"),
                "account_info": result.get("account_info"),
                "timestamp": datetime.now().isoformat()
            }
        elif result.get("checkpoint_required"):
            return {
                "success": False,
                "checkpoint_required": True,
                "message": result.get("message", "Verification required"),
                "account_id": result.get("account_id"),
                "timestamp": datetime.now().isoformat()
            }
        elif result.get("requires_api_key_update"):
            return {
                "success": False,
                "requires_api_key_update": True,
                "message": result.get("message", "API key update required"),
                "error": result.get("error"),
                "timestamp": datetime.now().isoformat()
            }
        else:
            # Connection failed
            raise HTTPException(
                status_code=400,
                detail=result.get("message", result.get("error", "Failed to connect account"))
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error connecting Instagram account: {e}")
        raise HTTPException(status_code=500, detail=str(e))

class InstagramCheckpointRequest(BaseModel):
    account_id: str = Field(..., description="Account ID from connection attempt")
    code: str = Field(..., description="Verification code")

@app.post("/api/instagram/solve-checkpoint")
async def solve_instagram_checkpoint(request: InstagramCheckpointRequest):
    """Solve Instagram checkpoint verification"""
    try:
        from instagram_integration.instagram_api import InstagramMessaging

        instagram = InstagramMessaging()
        if not instagram.unipile_client:
            raise HTTPException(status_code=400, detail="Unipile client not available")

        # Solve checkpoint using Unipile API
        checkpoint_data = {
            "account_id": request.account_id,
            "code": request.code
        }

        result = instagram.unipile_client._make_request("POST", f"accounts/{request.account_id}/checkpoint", checkpoint_data)

        if "error" in result:
            # Check if another checkpoint is required
            error_msg = result.get("error", "")
            # Handle case where error might be a dict
            if isinstance(error_msg, dict):
                error_msg = str(error_msg)
            elif not isinstance(error_msg, str):
                error_msg = str(error_msg)

            if "checkpoint" in error_msg.lower() or result.get("checkpoint_required"):
                return {
                    "success": False,
                    "checkpoint_required": True,
                    "checkpoint_type": result.get("checkpoint_type", "UNKNOWN"),
                    "message": result.get("error", "Additional verification required"),
                    "account_id": request.account_id,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                raise HTTPException(status_code=400, detail=result.get("error", "Checkpoint verification failed"))

        # Check if checkpoint was solved successfully
        if result.get("success") or result.get("status") == "connected":
            return {
                "success": True,
                "account_id": request.account_id,
                "message": "Instagram account connected successfully after checkpoint verification",
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": True,
                "message": "Checkpoint solved successfully",
                "result": result,
                "timestamp": datetime.now().isoformat()
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error solving Instagram checkpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))

class InstagramUnipileConnectRequest(BaseModel):
    api_key: str = Field(..., description="Unipile API key")

@app.post("/api/instagram/update-api-key")
async def update_instagram_api_key(request: InstagramUnipileConnectRequest):
    """Update Instagram Unipile API key"""
    try:
        from instagram_integration.instagram_api import InstagramMessaging

        instagram = InstagramMessaging()
        if not instagram.unipile_client:
            raise HTTPException(status_code=400, detail="Unipile client not available")

        # Use the improved update_api_key method
        result = instagram.update_api_key(request.api_key)

        if result.get("success"):
            return {
                "success": True,
                "message": result.get("message"),
                "accounts_found": result.get("accounts_found", 0),
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=400, detail=result.get("message", result.get("error", "Failed to update API key")))

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating Instagram API key: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/instagram/unipile/connect")
async def connect_instagram_unipile(request: InstagramUnipileConnectRequest):
    """Connect Instagram via Unipile API (legacy endpoint - use update-api-key instead)"""
    try:
        from instagram_integration.instagram_api import InstagramMessaging

        instagram = InstagramMessaging()
        if not instagram.unipile_client:
            raise HTTPException(status_code=400, detail="Unipile client not available")

        # Use the improved update_api_key method
        result = instagram.update_api_key(request.api_key)

        if result.get("success"):
            return {
                "success": True,
                "message": "Unipile API connected successfully",
                "accounts_found": result.get("accounts_found", 0),
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=400, detail=result.get("message", result.get("error", "Failed to connect to Unipile")))

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error connecting Instagram via Unipile: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/instagram/connection-status")
async def get_instagram_connection_status():
    """Get Instagram connection status"""
    try:
        from instagram_integration.instagram_api import InstagramMessaging

        instagram = InstagramMessaging()
        status = instagram.get_connection_status()

        return {
            "success": True,
            "status": status,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting Instagram connection status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

class InstagramMessageRequest(BaseModel):
    recipient_id: str = Field(..., description="Instagram user ID (IGSID)")
    message: str = Field(..., description="Message content")
    use_unipile: Optional[bool] = Field(default=True, description="Use Unipile API")

@app.post("/api/instagram/send-message")
async def send_instagram_message(request: InstagramMessageRequest):
    """Send Instagram message"""
    try:
        from instagram_integration.instagram_api import InstagramMessaging

        instagram = InstagramMessaging()
        result = instagram.send_message(request.recipient_id, request.message)

        if not result.get("success", False):
            raise HTTPException(status_code=400, detail=result.get("error", "Failed to send message"))

        return {
            "success": True,
            "result": result,
            "method": result.get("method", "unipile"),
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending Instagram message: {e}")
        raise HTTPException(status_code=500, detail=str(e))

class InstagramBulkRequest(BaseModel):
    recipients: List[str] = Field(..., description="List of Instagram user IDs")
    message: str = Field(..., description="Message content")
    delay: Optional[float] = Field(default=3.0, description="Delay between messages in seconds")

@app.post("/api/instagram/send-bulk")
async def send_instagram_bulk_messages(request: InstagramBulkRequest):
    """Send bulk Instagram messages"""
    try:
        from instagram_integration.instagram_api import InstagramMessaging

        instagram = InstagramMessaging()
        results = instagram.send_bulk_messages(request.recipients, request.message, request.delay)

        # Calculate summary
        total = len(results)
        successful = sum(1 for r in results if r.get("result", {}).get("success", False))

        return {
            "success": True,
            "summary": {
                "total": total,
                "successful": successful,
                "failed": total - successful
            },
            "results": results,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error sending Instagram bulk messages: {e}")
        raise HTTPException(status_code=500, detail=str(e))

class InstagramCommentReplyRequest(BaseModel):
    comment_id: str = Field(..., description="Instagram comment ID")
    reply_text: str = Field(..., description="Reply text")

@app.post("/api/instagram/reply-comment")
async def reply_to_instagram_comment(request: InstagramCommentReplyRequest):
    """Reply to Instagram comment"""
    try:
        from instagram_integration.instagram_api import InstagramMessaging

        instagram = InstagramMessaging()
        result = instagram.reply_to_comment(request.comment_id, request.reply_text)

        if not result.get("success", False):
            raise HTTPException(status_code=400, detail=result.get("error", "Failed to reply to comment"))

        return {
            "success": True,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error replying to Instagram comment: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/instagram/post-comments")
async def get_instagram_post_comments(post_id: str, limit: int = 25):
    """Get Instagram post comments"""
    try:
        from instagram_integration.instagram_api import InstagramMessaging

        instagram = InstagramMessaging()
        result = instagram.get_post_comments(post_id, limit)

        return {
            "success": True,
            "data": result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting Instagram post comments: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/instagram/conversations")
async def get_instagram_conversations(limit: int = 25):
    """Get Instagram conversations"""
    try:
        from instagram_integration.instagram_api import InstagramMessaging

        instagram = InstagramMessaging()
        result = instagram.get_conversations(limit)

        return {
            "success": True,
            "data": result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting Instagram conversations: {e}")
        raise HTTPException(status_code=500, detail=str(e))

class InstagramPostRequest(BaseModel):
    image_url: str = Field(..., description="Image URL")
    caption: Optional[str] = Field(None, description="Post caption")

@app.post("/api/instagram/create-post")
async def create_instagram_post(request: InstagramPostRequest):
    """Create Instagram post"""
    try:
        from instagram_integration.instagram_api import InstagramMessaging

        instagram = InstagramMessaging()
        result = instagram.create_media_post(request.image_url, request.caption)

        if not result.get("success", False):
            raise HTTPException(status_code=400, detail=result.get("error", "Failed to create post"))

        return {
            "success": True,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating Instagram post: {e}")
        raise HTTPException(status_code=500, detail=str(e))

class InstagramStoryRequest(BaseModel):
    media_url: str = Field(..., description="Media URL")
    media_type: Optional[str] = Field(default="image", description="Media type (image/video)")

@app.post("/api/instagram/create-story")
async def create_instagram_story(request: InstagramStoryRequest):
    """Create Instagram story"""
    try:
        from instagram_integration.instagram_api import InstagramMessaging

        instagram = InstagramMessaging()
        result = instagram.create_story(request.media_url, request.media_type)

        if not result.get("success", False):
            raise HTTPException(status_code=400, detail=result.get("error", "Failed to create story"))

        return {
            "success": True,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating Instagram story: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/instagram/account-info")
async def get_instagram_account_info():
    """Get Instagram account information"""
    try:
        from instagram_integration.instagram_api import InstagramMessaging

        instagram = InstagramMessaging()
        result = instagram.get_account_info()

        # Handle both success and error cases properly
        if "error" in result:
            # Return error in a structured format instead of raising exception
            return {
                "success": False,
                "error": result["error"],
                "message": "No Instagram account connected. Please connect an account first.",
                "timestamp": datetime.now().isoformat()
            }
        elif result.get("success", False):
            # Success case
            return {
                "success": True,
                "data": result,
                "timestamp": datetime.now().isoformat()
            }
        else:
            # Unexpected response format
            return {
                "success": False,
                "error": "Unexpected response format",
                "data": result,
                "timestamp": datetime.now().isoformat()
            }

    except Exception as e:
        logger.error(f"Error getting Instagram account info: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# WhatsApp-specific endpoints
@app.post("/api/whatsapp/authenticate")
async def authenticate_whatsapp():
    """Authenticate WhatsApp account and generate QR code"""
    try:
        from whatsapp_integration.whatsapp_api import WhatsAppMessaging
        whatsapp = WhatsAppMessaging()

        result = whatsapp.authenticate_account()

        if result.get("success", False):
            # Return the result directly with timestamp added
            response = result.copy()
            response["timestamp"] = datetime.now().isoformat()
            return response
        else:
            # Return error in expected format
            return {
                "success": False,
                "error": result.get("error", "Authentication failed"),
                "timestamp": datetime.now().isoformat()
            }

    except Exception as e:
        logger.error(f"Error authenticating WhatsApp: {e}")
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.get("/api/whatsapp/status")
async def get_whatsapp_status():
    """Get WhatsApp connection status"""
    try:
        from whatsapp_integration.whatsapp_api import WhatsAppMessaging
        whatsapp = WhatsAppMessaging()

        status = whatsapp.check_connection_status()

        return {
            "success": True,
            "status": status,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting WhatsApp status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

class WhatsAppConfigRequest(BaseModel):
    unipile_api_key: Optional[str] = Field(None, description="Unipile API key")
    webhook_settings: Optional[Dict[str, Any]] = Field(None, description="Webhook configuration")
    message_templates: Optional[Dict[str, str]] = Field(None, description="Message templates")

@app.post("/api/whatsapp/config")
async def update_whatsapp_config(request: WhatsAppConfigRequest):
    """Update WhatsApp configuration"""
    try:
        from whatsapp_integration.whatsapp_api import WhatsAppMessaging
        whatsapp = WhatsAppMessaging()

        config_data = {k: v for k, v in request.model_dump().items() if v is not None}
        result = whatsapp.update_config(**config_data)

        if not result.get("success", False):
            raise HTTPException(status_code=400, detail=result.get("error", "Configuration update failed"))

        return {
            "success": True,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating WhatsApp config: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/whatsapp/test")
async def test_whatsapp_connection():
    """Test WhatsApp connection"""
    try:
        from whatsapp_integration.whatsapp_api import WhatsAppMessaging
        whatsapp = WhatsAppMessaging()

        result = whatsapp.test_connection()

        return {
            "success": True,
            "test_result": result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error testing WhatsApp connection: {e}")
        raise HTTPException(status_code=500, detail=str(e))

class WhatsAppMessageRequest(BaseModel):
    phone_number: str = Field(..., description="Recipient phone number")
    message: str = Field(..., description="Message content")

@app.post("/api/whatsapp/send")
async def send_whatsapp_message(request: WhatsAppMessageRequest):
    """Send WhatsApp message directly"""
    try:
        from whatsapp_integration.whatsapp_api import WhatsAppMessaging
        whatsapp = WhatsAppMessaging()

        result = whatsapp.send_message(request.phone_number, request.message)

        if not result.get("success", False):
            raise HTTPException(status_code=400, detail=result.get("error", "Failed to send message"))

        return {
            "success": True,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending WhatsApp message: {e}")
        raise HTTPException(status_code=500, detail=str(e))

class WhatsAppBulkRequest(BaseModel):
    recipients: List[str] = Field(..., description="List of phone numbers (e.g., ['+1234567890', '+0987654321'])")
    message: str = Field(..., description="Message content")
    delay: Optional[float] = Field(default=2.0, description="Delay between messages in seconds")

@app.post("/api/whatsapp/bulk")
async def send_whatsapp_bulk(request: WhatsAppBulkRequest):
    """Send bulk WhatsApp messages to phone numbers"""
    try:
        from whatsapp_integration.whatsapp_api import WhatsAppMessaging
        whatsapp = WhatsAppMessaging()

        # Convert phone numbers to recipient format expected by send_bulk_messages
        recipients = [{"phone_number": phone} for phone in request.recipients]

        results = whatsapp.send_bulk_messages(
            recipients,
            request.message,
            request.delay
        )

        # Calculate summary
        total = len(results)
        successful = sum(1 for r in results if r.get("result", {}).get("success", False))

        return {
            "success": True,
            "summary": {
                "total": total,
                "successful": successful,
                "failed": total - successful
            },
            "results": results,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error sending WhatsApp bulk messages: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# WhatsApp Chat History endpoints
@app.get("/api/whatsapp/conversations")
async def get_whatsapp_conversations(limit: int = 50):
    """Get WhatsApp conversations/chats"""
    try:
        from whatsapp_integration.whatsapp_api import WhatsAppMessaging
        whatsapp = WhatsAppMessaging()

        result = whatsapp.get_conversations(limit)

        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])

        return {
            "success": True,
            "data": result,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting WhatsApp conversations: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/whatsapp/chat/{chat_id}/messages")
async def get_whatsapp_chat_messages(chat_id: str, limit: int = 50):
    """Get messages from a specific WhatsApp chat"""
    try:
        from whatsapp_integration.whatsapp_api import WhatsAppMessaging
        whatsapp = WhatsAppMessaging()

        result = whatsapp.get_chat_messages(chat_id, limit)

        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])

        return {
            "success": True,
            "data": result,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting WhatsApp chat messages: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/whatsapp/chat/{chat_id}/details")
async def get_whatsapp_conversation_details(chat_id: str):
    """Get detailed information about a WhatsApp conversation including participant phone numbers"""
    try:
        from whatsapp_integration.whatsapp_api import WhatsAppMessaging
        whatsapp = WhatsAppMessaging()

        result = whatsapp.get_conversation_details(chat_id)

        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])

        return {
            "success": True,
            "data": result,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting WhatsApp conversation details: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/whatsapp/history")
async def get_whatsapp_chat_history(phone_number: str = None, limit: int = 50):
    """Get WhatsApp chat history for a specific phone number or all conversations"""
    try:
        from whatsapp_integration.whatsapp_api import WhatsAppMessaging
        whatsapp = WhatsAppMessaging()

        result = whatsapp.get_chat_history(phone_number, limit)

        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])

        return {
            "success": True,
            "data": result,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting WhatsApp chat history: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Telegram-specific endpoints

# QR Code Authentication endpoints
@app.post("/api/telegram/authenticate")
async def authenticate_telegram_account(request: Request):
    """Authenticate Telegram account via QR code"""
    try:
        from telegram_integration.telegram_api import TelegramMessaging
        telegram = TelegramMessaging()

        # Check for force parameter in query string
        force_new = request.query_params.get("force", "false").lower() == "true"

        result = telegram.authenticate_account(force_new=force_new)

        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])

        return {
            "success": True,
            "data": result,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error authenticating Telegram account: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/telegram/status")
async def get_telegram_status():
    """Get Telegram connection status"""
    try:
        from telegram_integration.telegram_api import TelegramMessaging
        telegram = TelegramMessaging()

        result = telegram.check_qr_connection_status()

        return {
            "success": True,
            "data": result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting Telegram status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Telegram configuration is now handled via QR code authentication only

# Telegram messaging endpoints
class TelegramSendRequest(BaseModel):
    chat_id: str = Field(..., description="Chat ID or username")
    message: str = Field(..., description="Message to send")

class TelegramBulkRequest(BaseModel):
    recipients: List[str] = Field(..., description="List of chat IDs or usernames")
    message: str = Field(..., description="Message to send")

@app.post("/api/telegram/send-test")
async def send_telegram_test_message(request: TelegramSendRequest):
    """Send test message via Telegram"""
    try:
        from telegram_integration.telegram_api import TelegramMessaging
        telegram = TelegramMessaging()

        if not telegram.is_configured():
            raise HTTPException(status_code=400, detail="Telegram not connected. Please authenticate first.")

        result = telegram.send_message(request.chat_id, request.message)

        if result.success:
            return {
                "success": True,
                "data": {
                    "message_id": result.message_id,
                    "platform": result.platform,
                    "method": result.method
                },
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=400, detail=result.error)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending Telegram test message: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/telegram/send-bulk")
async def send_telegram_bulk_messages(request: TelegramBulkRequest):
    """Send bulk messages via Telegram"""
    try:
        from telegram_integration.telegram_api import TelegramMessaging
        telegram = TelegramMessaging()

        if not telegram.is_configured():
            raise HTTPException(status_code=400, detail="Telegram not connected. Please authenticate first.")

        results = []
        for recipient in request.recipients:
            result = telegram.send_message(recipient, request.message)
            results.append({
                "recipient": recipient,
                "success": result.success,
                "message_id": result.message_id if result.success else None,
                "error": result.error if not result.success else None
            })

        successful = sum(1 for r in results if r["success"])
        failed = len(results) - successful

        return {
            "success": True,
            "data": {
                "total": len(results),
                "successful": successful,
                "failed": failed,
                "results": results
            },
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending Telegram bulk messages: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/telegram/chats")
async def get_telegram_chats():
    """Get list of Telegram chats"""
    try:
        from telegram_integration.telegram_api import TelegramMessaging
        telegram = TelegramMessaging()

        if not telegram.is_configured():
            raise HTTPException(status_code=400, detail="Telegram not connected. Please authenticate first.")

        # Get chats from Unipile API
        result = telegram.get_chats()

        if result.get("success"):
            return {
                "success": True,
                "data": {
                    "chats": result.get("chats", [])
                },
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=400, detail=result.get("error", "Failed to get chats"))

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting Telegram chats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/telegram/chat-history/{chat_id}")
async def get_telegram_chat_history(chat_id: str):
    """Get chat history for a specific Telegram chat"""
    try:
        from telegram_integration.telegram_api import TelegramMessaging
        telegram = TelegramMessaging()

        if not telegram.is_configured():
            raise HTTPException(status_code=400, detail="Telegram not connected. Please authenticate first.")

        # Get chat history from Unipile API
        result = telegram.get_chat_history(chat_id)

        if result.get("success"):
            return {
                "success": True,
                "data": {
                    "messages": result.get("messages", []),
                    "chat_id": chat_id
                },
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=400, detail=result.get("error", "Failed to get chat history"))

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting Telegram chat history: {e}")
        raise HTTPException(status_code=500, detail=str(e))



class TelegramTestMessageRequest(BaseModel):
    chat_id: str = Field(..., description="Chat ID or username")
    message: str = Field(..., description="Test message")

@app.post("/api/telegram/send-test")
async def send_telegram_test_message(request: TelegramTestMessageRequest):
    """Send Telegram test message"""
    try:
        from telegram_integration.telegram_api import TelegramMessaging
        telegram = TelegramMessaging()

        result = telegram.send_message(request.chat_id, request.message)

        # Handle MessageResult object (not dictionary)
        if result.success:
            return {
                "success": True,
                "result": {
                    "success": result.success,
                    "message_id": result.message_id,
                    "platform": result.platform,
                    "method": result.method,
                    "timestamp": result.timestamp.isoformat() if result.timestamp else datetime.now().isoformat()
                },
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=400, detail=result.error or "Failed to send test message")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending Telegram test message: {e}")
        raise HTTPException(status_code=500, detail=str(e))

class TelegramBulkMessageRequest(BaseModel):
    recipients: List[str] = Field(..., description="List of chat IDs or usernames")
    message: str = Field(..., description="Message content")
    delay: Optional[float] = Field(default=1.0, description="Delay between messages in seconds")

@app.post("/api/telegram/send-bulk")
async def send_telegram_bulk_messages(request: TelegramBulkMessageRequest):
    """Send bulk Telegram messages"""
    try:
        from telegram_integration.telegram_api import TelegramMessaging
        telegram = TelegramMessaging()

        results = telegram.send_bulk_messages(request.recipients, request.message, request.delay)

        # Calculate summary - handle MessageResult objects properly
        total = len(results)
        successful = 0

        for r in results:
            result_data = r.get("result", {})
            # Check if it's a MessageResult object or dict
            if isinstance(result_data, dict):
                if result_data.get("success"):
                    successful += 1
            else:
                # It's a MessageResult object
                if hasattr(result_data, 'success') and result_data.success:
                    successful += 1

        return {
            "success": True,
            "summary": {
                "total": total,
                "successful": successful,
                "failed": total - successful
            },
            "results": results,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error sending Telegram bulk messages: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/telegram/updates")
async def get_telegram_updates():
    """Get recent Telegram updates (conflict-safe)"""
    try:
        # Use the bot polling manager to safely get updates
        result = bot_polling_manager.safe_get_updates()

        if result.get("success"):
            return {
                "success": True,
                "data": result["data"],
                "cached": result.get("cached", False),
                "fresh": result.get("fresh", False),
                "conflicts_prevented": bot_polling_manager.stats.get("conflicts_prevented", 0),
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=400, detail=result.get("error", "Failed to get updates"))

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting Telegram updates: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/telegram/updates/fresh")
async def get_fresh_telegram_updates():
    """Force get fresh Telegram updates (may cause conflicts if polling is active)"""
    try:
        # Force fresh updates even if polling is running
        result = bot_polling_manager.safe_get_updates(force=True)

        if result.get("success"):
            return {
                "success": True,
                "data": result["data"],
                "forced": True,
                "warning": "This endpoint may cause conflicts if used while polling is active",
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=400, detail=result.get("error", "Failed to get fresh updates"))

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting fresh Telegram updates: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/telegram/process-updates")
async def process_telegram_updates():
    """Process pending updates and respond to messages"""
    try:
        from telegram_integration.telegram_api import TelegramMessaging
        telegram = TelegramMessaging()

        # Get updates
        updates_result = telegram.get_updates()

        if not updates_result.get("ok"):
            raise HTTPException(status_code=400, detail=updates_result.get("description", "Failed to get updates"))

        updates = updates_result.get("result", [])

        if not updates:
            return {
                "success": True,
                "message": "No new messages to process",
                "processed": 0,
                "timestamp": datetime.now().isoformat()
            }

        # Process updates and send responses
        responses = telegram.process_updates(updates)

        # Mark messages as processed
        if updates:
            last_update_id = updates[-1]["update_id"]
            telegram.get_updates(offset=last_update_id + 1, limit=1)

        # Calculate statistics - handle both dict and MessageResult responses
        successful_responses = 0
        for r in responses:
            # The responses from process_updates are dictionaries, not MessageResult objects
            if isinstance(r, dict) and r.get("sent_successfully", False):
                successful_responses += 1

        return {
            "success": True,
            "message": f"Processed {len(updates)} updates, sent {successful_responses} responses",
            "processed": len(updates),
            "responses_sent": successful_responses,
            "details": responses,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing Telegram updates: {e}")
        raise HTTPException(status_code=500, detail=str(e))

class TelegramPollingRequest(BaseModel):
    action: str = Field(..., description="Action: 'start' or 'stop'")
    interval: Optional[float] = Field(default=2.0, description="Polling interval in seconds")

@app.post("/api/telegram/polling")
async def control_telegram_polling(request: TelegramPollingRequest):
    """Start or stop Telegram message polling (integrated with main server)"""
    try:
        if request.action == "start":
            result = bot_polling_manager.start_polling(request.interval)
            if result["success"]:
                return {
                    "success": True,
                    "message": "✅ Bot polling started successfully! Your bot will now automatically respond to messages.",
                    "bot_info": result.get("bot_info", {}),
                    "interval": request.interval,
                    "action": "start",
                    "timestamp": datetime.now().isoformat()
                }
            else:
                raise HTTPException(status_code=400, detail=result["error"])

        elif request.action == "stop":
            result = bot_polling_manager.stop_polling()
            if result["success"]:
                return {
                    "success": True,
                    "message": "🛑 Bot polling stopped successfully",
                    "stats": result.get("stats", {}),
                    "action": "stop",
                    "timestamp": datetime.now().isoformat()
                }
            else:
                raise HTTPException(status_code=400, detail=result["error"])
        else:
            raise HTTPException(status_code=400, detail="Invalid action. Use 'start' or 'stop'")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error controlling Telegram polling: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/telegram/polling/status")
async def get_telegram_polling_status():
    """Get current bot polling status and statistics"""
    try:
        status = bot_polling_manager.get_status()
        return {
            "success": True,
            "polling": status,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error getting polling status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Telegram Unipile integration endpoints
class TelegramUnipileConnectRequest(BaseModel):
    api_key: str = Field(..., description="Unipile API key")

@app.post("/api/telegram/unipile/connect")
async def connect_telegram_unipile(request: TelegramUnipileConnectRequest):
    """Connect Telegram via Unipile API"""
    try:
        from telegram_integration.telegram_api import UnipileClient

        # Test the API key by making a request
        unipile = UnipileClient(request.api_key)
        accounts = unipile.make_request("GET", "accounts")

        if "error" in accounts:
            raise HTTPException(status_code=400, detail=f"Failed to connect to Unipile: {accounts['error']}")

        # Save the API key (you might want to encrypt this in production)
        # For now, we'll just validate it works

        return {
            "success": True,
            "message": "Unipile API connected successfully",
            "accounts_found": len(accounts.get("items", [])),
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error connecting Telegram via Unipile: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/telegram/unipile/status")
async def get_telegram_unipile_status():
    """Get Telegram Unipile connection status and accounts"""
    try:
        from telegram_integration.telegram_api import UnipileClient

        # Use the default API key (in production, load from secure storage)
        unipile = UnipileClient("K0Py2YdG.SazddZm5laRo0Bk9kZ0cKnNt8PLt/AJj15NEGsM7lrk=")
        accounts = unipile.make_request("GET", "accounts")

        if "error" in accounts:
            raise HTTPException(status_code=400, detail=f"Failed to get Unipile status: {accounts['error']}")

        # Filter for Telegram accounts
        all_accounts = accounts.get("items", [])
        telegram_accounts = [acc for acc in all_accounts if acc.get("type") == "TELEGRAM"]

        return {
            "success": True,
            "connected": len(telegram_accounts) > 0,
            "accounts": telegram_accounts,
            "total_accounts": len(all_accounts),
            "telegram_accounts": len(telegram_accounts),
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting Telegram Unipile status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/telegram-helper")
async def telegram_helper():
    """Serve the Telegram chat ID helper page"""
    try:
        import os
        helper_path = os.path.join(os.path.dirname(__file__), "..", "telegram_chat_helper.html")

        if os.path.exists(helper_path):
            with open(helper_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return HTMLResponse(content=content)
        else:
            raise HTTPException(status_code=404, detail="Helper page not found")
    except Exception as e:
        logger.error(f"Error serving Telegram helper: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
