"""
Instagram Messaging API Integration
Handles authentication and messaging for Instagram Business/Creator accounts
Uses Unipile API for unified Instagram messaging
Focus on comment replies and story interactions due to Instagram's messaging limitations
"""

import json
import time
from typing import Dict, List
import logging
from datetime import datetime
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from unipile_api import UnipileAPI

class InstagramMessaging:
    def __init__(self, config_path: str = None):
        """Initialize Instagram API client with Unipile"""
        # Determine config path based on current working directory
        if config_path is None:
            import os
            current_dir = os.getcwd()
            if current_dir.endswith('integrations'):
                # Running from integrations directory
                config_path = "instagram_integration/config.json"
            else:
                # Running from root directory
                config_path = "integrations/instagram_integration/config.json"

        self.config_path = config_path

        # Setup logging first
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # Load config after logger is initialized
        self.config = self._load_config()

        # Unipile API setup
        self.unipile_client = None
        try:
            # Get API key from config or use default
            unipile_config = self.config.get("unipile", {})
            api_key = unipile_config.get("api_key", "iGJsHDIR.sr6Pg30nB1cKUPC6eDY8tzVx+Opu+t+c6wQiKAEz2Ek=")
            base_url = unipile_config.get("api_url", "https://api8.unipile.com:13814")

            if not api_key:
                api_key = "iGJsHDIR.sr6Pg30nB1cKUPC6eDY8tzVx+Opu+t+c6wQiKAEz2Ek="
                self.logger.warning("No API key found in config, using default")

            self.unipile_client = UnipileAPI(api_key=api_key, base_url=base_url)
            self.logger.info("Unipile API client initialized for Instagram")
        except Exception as e:
            self.logger.error(f"Failed to initialize Unipile API: {e}")
            raise Exception("Unipile API is required for Instagram integration")

        # Rate limiting for Unipile API
        self.last_request_time = 0
        self.min_request_interval = 1 / self.config.get("rate_limit", {}).get("messages_per_second", 5)

        # Account connection status
        self.connection_status = {"unipile": False}
    
    def _load_config(self) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            if hasattr(self, 'logger'):
                self.logger.warning(f"Config file not found: {self.config_path}")
            return {}
        except json.JSONDecodeError:
            if hasattr(self, 'logger'):
                self.logger.error(f"Invalid JSON in config file: {self.config_path}")
            return {}
    
    def _save_config(self):
        """Save configuration to JSON file"""
        try:
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.error(f"Error saving config: {e}")
    
    def _rate_limit(self):
        """Implement rate limiting for Unipile API calls"""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time

        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            time.sleep(sleep_time)

        self.last_request_time = time.time()

    def authenticate_account(self, account_id: str = None) -> Dict:
        """Authenticate Instagram account via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Check if account is already connected
            accounts = self.unipile_client.get_accounts()

            # Handle error response
            if "error" in accounts:
                return {
                    "success": False,
                    "error": accounts["error"],
                    "message": "Failed to check connected accounts. Please verify your Unipile API key."
                }

            instagram_accounts = [acc for acc in accounts.get("items", [])
                                if acc.get("type", "").upper() == "INSTAGRAM"]

            if instagram_accounts:
                self.connection_status["unipile"] = True
                self.logger.info("Instagram account already connected via Unipile")
                return {
                    "success": True,
                    "message": "Instagram account connected",
                    "accounts": instagram_accounts
                }
            else:
                # Return authentication URL or instructions
                return {
                    "success": False,
                    "message": "No Instagram account connected. Please connect via Unipile dashboard or use the connect-account endpoint.",
                    "auth_required": True,
                    "unipile_dashboard": "https://app.unipile.com/dashboard"
                }
        except Exception as e:
            self.logger.error(f"Authentication error: {e}")
            return {"error": str(e)}

    def connect_account(self, username: str, password: str) -> Dict:
        """Connect Instagram account using credentials via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Prepare connection data
            connect_data = {
                "provider": "INSTAGRAM",
                "username": username,
                "password": password
            }

            self.logger.info(f"Attempting to connect Instagram account: {username}")
            result = self.unipile_client._make_request("POST", "api/v1/accounts", connect_data)

            if "error" in result:
                error_msg = result.get("error", "Unknown error")

                # Handle different types of errors
                if "403" in str(error_msg) or "Forbidden" in str(error_msg):
                    return {
                        "success": False,
                        "error": "Account creation not permitted",
                        "message": "Your Unipile API key doesn't have permission to create new accounts. Please connect your Instagram account via the Unipile dashboard at https://app.unipile.com/dashboard, then refresh this page.",
                        "requires_dashboard_connection": True,
                        "unipile_dashboard": "https://app.unipile.com/dashboard"
                    }
                elif "checkpoint" in str(error_msg).lower():
                    return {
                        "success": False,
                        "checkpoint_required": True,
                        "message": "Instagram requires verification. Please check your Instagram app for a verification code.",
                        "account_id": result.get("account_id"),
                        "error": error_msg
                    }
                else:
                    return {
                        "success": False,
                        "error": error_msg,
                        "message": "Failed to connect Instagram account. Please check your credentials."
                    }

            # Success case
            if result.get("id") or result.get("account_id"):
                account_id = result.get("id") or result.get("account_id")
                self.logger.info(f"Instagram account connected successfully: {account_id}")

                # Update connection status
                self.connection_status["unipile"] = True

                return {
                    "success": True,
                    "account_id": account_id,
                    "message": "Instagram account connected successfully",
                    "account_info": result
                }
            else:
                return {
                    "success": False,
                    "error": "Unexpected response format",
                    "message": "Account connection may have succeeded but response format is unexpected.",
                    "raw_response": result
                }

        except Exception as e:
            self.logger.error(f"Error connecting Instagram account: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "An error occurred while connecting the Instagram account."
            }

    def send_message(self, recipient_id: str, text: str, **kwargs) -> Dict:
        """Send Instagram message via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        self._rate_limit()

        try:
            result = self.unipile_client.send_instagram_message(recipient_id, text, **kwargs)
            if "error" not in result:
                self.logger.info(f"Message sent via Unipile to {recipient_id}")
                return {"success": True, "result": result, "method": "unipile"}
            else:
                self.logger.error(f"Unipile message failed: {result.get('error')}")
                return {"error": result.get('error')}
        except Exception as e:
            self.logger.error(f"Unipile error: {e}")
            return {"error": str(e)}

    def send_bulk_messages(self, recipients: List[str], message: str, delay: float = 3.0) -> List[Dict]:
        """Send message to multiple recipients via Unipile"""
        if not self.unipile_client:
            return [{"error": "Unipile client not available"}]

        results = []

        for recipient_id in recipients:
            result = self.send_message(recipient_id, message)
            results.append({
                "recipient_id": recipient_id,
                "result": result,
                "method": "unipile",
                "timestamp": datetime.now().isoformat()
            })

            if delay > 0:
                time.sleep(delay)

        return results

    def get_connection_status(self) -> Dict:
        """Get current connection status for Unipile"""
        status = {
            "unipile": {
                "available": bool(self.unipile_client),
                "connected": False,
                "accounts": []
            }
        }

        # Check Unipile connection
        if self.unipile_client:
            try:
                accounts = self.unipile_client.get_accounts()
                # Handle error response
                if "error" in accounts:
                    self.logger.error(f"Unipile API error: {accounts['error']}")
                    status["unipile"]["connected"] = False
                    status["unipile"]["accounts"] = []
                else:
                    # Use correct response format - Unipile returns "items" not "accounts"
                    instagram_accounts = [acc for acc in accounts.get("items", [])
                                        if acc.get("type", "").upper() == "INSTAGRAM"]
                    status["unipile"]["connected"] = len(instagram_accounts) > 0
                    status["unipile"]["accounts"] = instagram_accounts
                    self.connection_status["unipile"] = len(instagram_accounts) > 0
            except Exception as e:
                self.logger.error(f"Error checking Unipile status: {e}")
                status["unipile"]["connected"] = False
                status["unipile"]["accounts"] = []

        return status

    def get_account_info(self) -> Dict:
        """Get Instagram account information via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            self.logger.info("Fetching accounts from Unipile API...")
            accounts = self.unipile_client.get_accounts()
            self.logger.info(f"Unipile accounts response: {accounts}")

            # Handle error response
            if "error" in accounts:
                self.logger.error(f"Unipile API error: {accounts['error']}")
                return {"error": accounts["error"]}

            # Use correct response format - Unipile returns "items" not "accounts"
            all_accounts = accounts.get("items", accounts.get("accounts", []))
            self.logger.info(f"Total accounts found: {len(all_accounts)}")

            instagram_accounts = [acc for acc in all_accounts
                                if acc.get("type", "").upper() == "INSTAGRAM"]

            self.logger.info(f"Instagram accounts found: {len(instagram_accounts)}")

            if instagram_accounts:
                # Return the first Instagram account info
                account = instagram_accounts[0]
                self.logger.info(f"Using Instagram account: {account.get('id', 'N/A')}")

                return {
                    "success": True,
                    "account": account,
                    "id": account.get("account_id") or account.get("id"),
                    "username": account.get("username", "N/A"),
                    "name": account.get("name", "N/A"),
                    "provider": "instagram",
                    "debug_info": {
                        "total_accounts": len(all_accounts),
                        "instagram_accounts": len(instagram_accounts),
                        "account_keys": list(account.keys()) if account else []
                    }
                }
            else:
                # Provide detailed debug information
                account_types = [acc.get("type", "Unknown") for acc in all_accounts]
                self.logger.warning(f"No Instagram accounts found. Account types: {account_types}")

                return {
                    "error": "No Instagram account connected via Unipile",
                    "debug_info": {
                        "total_accounts": len(all_accounts),
                        "account_types": account_types,
                        "all_accounts": all_accounts  # Include for debugging
                    }
                }
        except Exception as e:
            self.logger.error(f"Error getting account info: {e}")
            return {"error": str(e)}

    def send_media_message(self, recipient_id: str, media_url: str,
                          media_type: str = "image", caption: str = None) -> Dict:
        """Send media message (image/video) to Instagram user via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        if media_type not in ["image", "video"]:
            return {"error": "Media type must be 'image' or 'video'"}

        self._rate_limit()

        try:
            # Use Unipile to send media message
            result = self.unipile_client.send_instagram_message(
                recipient_id,
                caption or "",
                media_url=media_url,
                media_type=media_type
            )

            if "error" not in result:
                self.logger.info(f"Media message sent via Unipile to {recipient_id}")
                return {"success": True, "result": result, "method": "unipile"}
            else:
                self.logger.error(f"Failed to send media message: {result}")
                return {"error": result.get('error')}
        except Exception as e:
            self.logger.error(f"Media message error: {e}")
            return {"error": str(e)}

    def get_conversations(self, limit: int = 25) -> Dict:
        """Get Instagram conversations via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Use Unipile to get conversations
            result = self.unipile_client.get_conversations("instagram", limit=limit)
            return {"success": True, "data": result}
        except Exception as e:
            self.logger.error(f"Error getting conversations: {e}")
            return {"error": str(e)}

    def get_messages(self, conversation_id: str, limit: int = 25) -> Dict:
        """Get messages from a conversation via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Use Unipile to get messages
            result = self.unipile_client.get_messages(conversation_id, limit=limit)
            return {"success": True, "data": result}
        except Exception as e:
            self.logger.error(f"Error getting messages: {e}")
            return {"error": str(e)}

    def create_media_post(self, image_url: str, caption: str = None) -> Dict:
        """Create Instagram media post via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Use Unipile to create Instagram post
            result = self.unipile_client.create_instagram_post(
                media_url=image_url,
                caption=caption or ""
            )

            if "error" not in result:
                self.logger.info("Instagram post created successfully via Unipile")
                return {"success": True, "result": result, "method": "unipile"}
            else:
                self.logger.error(f"Failed to create Instagram post: {result}")
                return {"error": result.get('error')}
        except Exception as e:
            self.logger.error(f"Post creation error: {e}")
            return {"error": str(e)}

    def create_story(self, media_url: str, media_type: str = "image") -> Dict:
        """Create Instagram story via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        if media_type not in ["image", "video"]:
            return {"error": "Media type must be 'image' or 'video'"}

        try:
            # Use Unipile to create Instagram story
            result = self.unipile_client.create_instagram_story(
                media_url=media_url,
                media_type=media_type
            )

            if "error" not in result:
                self.logger.info("Instagram story created successfully via Unipile")
                return {"success": True, "result": result, "method": "unipile"}
            else:
                self.logger.error(f"Failed to create Instagram story: {result}")
                return {"error": result.get('error')}
        except Exception as e:
            self.logger.error(f"Story creation error: {e}")
            return {"error": str(e)}

    def reply_to_comment(self, comment_id: str, reply_text: str) -> Dict:
        """Reply to a comment on Instagram post (recommended for engagement)"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Use Unipile to reply to comment
            result = self.unipile_client.reply_to_instagram_comment(comment_id, reply_text)

            if "error" not in result:
                self.logger.info(f"Comment reply sent successfully via Unipile to {comment_id}")
                return {"success": True, "result": result, "method": "unipile"}
            else:
                self.logger.error(f"Failed to reply to comment: {result}")
                return {"error": result.get('error')}
        except Exception as e:
            self.logger.error(f"Comment reply error: {e}")
            return {"error": str(e)}

    def get_post_comments(self, media_id: str, limit: int = 25) -> Dict:
        """Get comments on a specific Instagram post via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Use Unipile to get post comments
            result = self.unipile_client.get_instagram_post_comments(media_id, limit=limit)
            return {"success": True, "data": result}
        except Exception as e:
            self.logger.error(f"Error getting post comments: {e}")
            return {"error": str(e)}

    def get_story_mentions(self, limit: int = 25) -> Dict:
        """Get story mentions for the Instagram account via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Use Unipile to get story mentions
            result = self.unipile_client.get_instagram_story_mentions(limit=limit)
            return {"success": True, "data": result}
        except Exception as e:
            self.logger.error(f"Error getting story mentions: {e}")
            return {"error": str(e)}

    def respond_to_story_mention(self, story_id: str, response_text: str) -> Dict:
        """Respond to a story mention via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Use Unipile to respond to story mention
            result = self.unipile_client.respond_to_instagram_story(story_id, response_text)

            if "error" not in result:
                self.logger.info(f"Story mention response sent successfully via Unipile")
                return {"success": True, "result": result, "method": "unipile"}
            else:
                self.logger.error(f"Failed to respond to story mention: {result}")
                return {"error": result.get('error')}
        except Exception as e:
            self.logger.error(f"Story mention response error: {e}")
            return {"error": str(e)}

    def get_hashtag_mentions(self, hashtag: str, limit: int = 25) -> Dict:
        """Get recent posts mentioning a specific hashtag via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Use Unipile to search hashtag mentions
            result = self.unipile_client.search_instagram_hashtag(hashtag, limit=limit)
            return {"success": True, "data": result}
        except Exception as e:
            self.logger.error(f"Error getting hashtag mentions: {e}")
            return {"error": str(e)}

    def send_template_message(self, recipient_id: str, template_name: str,
                            **kwargs) -> Dict:
        """Send predefined template message via Unipile"""
        templates = self.config.get("message_templates", {})

        if template_name not in templates:
            return {"error": f"Template '{template_name}' not found"}

        message = templates[template_name].format(**kwargs)

        # Send via Unipile
        return self.send_message(recipient_id, message)

    def is_configured(self) -> bool:
        """Check if Instagram API is properly configured"""
        return bool(self.unipile_client)

    def update_config(self, unipile_api_key: str = None, **kwargs):
        """Update configuration"""
        if unipile_api_key:
            # Ensure unipile config section exists
            if "unipile" not in self.config:
                self.config["unipile"] = {}

            self.config["unipile"]["api_key"] = unipile_api_key
            # Update the Unipile client with new API key
            if self.unipile_client:
                self.unipile_client.api_key = unipile_api_key
                self.unipile_client.headers["X-API-KEY"] = unipile_api_key
                self.logger.info("Unipile API key updated")

        # Update any other config values
        for key, value in kwargs.items():
            if key in self.config:
                self.config[key] = value

        self._save_config()
        self.logger.info("Instagram configuration updated")

    def update_api_key(self, api_key: str) -> Dict:
        """Update Unipile API key and test connection"""
        try:
            # Update the API key
            self.update_config(unipile_api_key=api_key)

            # Test the new API key
            test_result = self.unipile_client.get_accounts()

            if "error" in test_result:
                return {
                    "success": False,
                    "error": test_result["error"],
                    "message": "API key updated but connection test failed. Please verify the API key is correct."
                }

            return {
                "success": True,
                "message": "API key updated and tested successfully",
                "accounts_found": len(test_result.get("items", []))
            }

        except Exception as e:
            self.logger.error(f"Error updating API key: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to update API key"
            }

# Alias for backward compatibility
InstagramAPI = InstagramMessaging

# Example usage
if __name__ == "__main__":
    # Initialize Instagram messaging with Unipile
    instagram = InstagramMessaging()

    # Check connection status
    status = instagram.get_connection_status()
    print(f"Connection status: {status}")

    # Check if configured
    if not instagram.is_configured():
        print("Instagram API not configured. Please ensure Unipile API is available.")
    else:
        # Example: Get account info
        account_info = instagram.get_account_info()
        print(f"Account info: {account_info}")

        # Example: Test authentication
        auth_result = instagram.authenticate_account()
        print(f"Authentication result: {auth_result}")

        # Example: Send a test message via Unipile (replace with actual user ID)
        # result = instagram.send_message("USER_ID", "Hello from Instagram via Unipile!")
        # print(f"Message result: {result}")

        # Example: Reply to comment (recommended approach)
        # reply_result = instagram.reply_to_comment("COMMENT_ID", "Thanks for your comment! 😊")
        # print(f"Comment reply result: {reply_result}")
